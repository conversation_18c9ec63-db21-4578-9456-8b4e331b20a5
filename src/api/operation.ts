import {
  Announcement,
  Carousel,
  ContentCategory,
  FrontContent,
  MailTemplate,
  MailTemplateCategory,
  MailTemplateType,
  MailTemplateVariable
} from '@/types/operation';

import apiRequest, { type IResponseDataPage } from './services';

export const getAnnouncement = () => {
  return apiRequest().get<Announcement[]>('/system/content/list');
};

type AnnouncementFormValue = Pick<
  Announcement,
  'contentMapping' | 'order' | 'status' | 'publishType' | 'startTime' | 'endTime'
>;

export const createAnnouncement = (params: AnnouncementFormValue) => {
  return apiRequest().post<Announcement>('/system/content/create', params);
};

export const updateAnnouncement = (params: AnnouncementFormValue & Pick<Announcement, 'id'>) => {
  return apiRequest().put<Announcement>('/system/content/edit', params);
};

export const updateAnnouncementOrder = (params: { orders: number[] }) => {
  return apiRequest().put<Announcement>('/system/content/edit/order', params);
};

export const deleteAnnouncement = (params: { id: number }) => {
  return apiRequest().delete('/system/content/del', params);
};

export const updateAnnouncementStatus = (params: { id: number; status: number }) => {
  return apiRequest().put<Announcement>('/system/content/edit', params);
};

// Front Content API functions
export const getFrontContent = () => {
  return apiRequest().get<FrontContent[]>('/system/content/front/list');
};

type FrontContentFormValue = Pick<
  FrontContent,
  'contentMapping' | 'order' | 'status' | 'categoryId'
>;

export const createFrontContent = (params: FrontContentFormValue) => {
  return apiRequest().post<FrontContent>('/system/content/front/create', params);
};

export const updateFrontContent = (params: FrontContentFormValue & Pick<FrontContent, 'id'>) => {
  return apiRequest().put<FrontContent>('/system/content/front/edit', params);
};

export const updateFrontContentOrder = (params: { orders: number[] }) => {
  return apiRequest().put<FrontContent>('/system/content/front/edit/order', params);
};

export const deleteFrontContent = (params: { id: number }) => {
  return apiRequest().delete('/system/content/front/del', params);
};

export const updateFrontContentStatus = (params: { id: number; status: number }) => {
  return apiRequest().put<FrontContent>('/system/content/front/edit', params);
};

// Content Category API functions
export const getContentCategoryList = () => {
  return apiRequest().get<IResponseDataPage<ContentCategory>>('/system/content/category/list');
};

export const getContentCategoryDetails = (params: { key: string }) => {
  return apiRequest().get<ContentCategory>('/system/content/category/details', { params });
};

type ContentCategoryFormValue = {
  key: string;
  name: string;
  description: string;
};

export const createContentCategory = (params: ContentCategoryFormValue) => {
  return apiRequest().post<ContentCategory>('/system/content/category/create', params);
};

export const updateContentCategory = (params: ContentCategoryFormValue & { id: number }) => {
  return apiRequest().put<ContentCategory>('/system/content/category/edit', params);
};

export const deleteContentCategory = (params: { id: number }) => {
  return apiRequest().delete('/system/content/category/del', params);
};

// Mail Template API functions
export const getMailTemplateList = () => {
  return apiRequest().get<IResponseDataPage<MailTemplate>>('/system/mail/template/list');
};

export const getMailTemplateDetails = (params: { id: number }) => {
  return apiRequest().get<MailTemplate>('/system/mail/template/details', { params });
};

export const getMailTemplateCategories = () => {
  return apiRequest().get<MailTemplateCategory[]>('/system/mail/category');
};

export const getMailTemplateTypes = () => {
  return apiRequest().get<MailTemplateType[]>('/system/mail/type');
};

export const getMailTemplateVariables = () => {
  return apiRequest().get<MailTemplateVariable[]>('/system/mail/variables');
};

type MailTemplateFormValue = {
  template_id: string;
  title: string;
  content: string;
  description: string;
  category: number;
  type: number;
};

export const createMailTemplate = (params: MailTemplateFormValue) => {
  return apiRequest().post<MailTemplate>('/system/mail/template/create', params);
};

export const updateMailTemplate = (params: MailTemplateFormValue & Pick<MailTemplate, 'id'>) => {
  return apiRequest().put<MailTemplate>('/system/mail/template/edit', params);
};

export const updateMailTemplateOrder = (params: { orders: number[] }) => {
  return apiRequest().put<MailTemplate>('/system/mail/template/edit/order', params);
};

export const deleteMailTemplate = (params: { id: number }) => {
  return apiRequest().delete('/system/mail/template/del', params);
};

export const getCarousel = () => {
  return apiRequest().get<IResponseDataPage<Carousel>>('/system/caro/list');
};

export const createCarousel = (params: {
  name: string;
  interact: number;
  link?: string;
  status: 1 | 0;
  publishType: number;
  startTime?: number;
  endTime?: number;
  zhTwBgPhoto?: File | string;
  zhTwTxtPhoto?: File | string;
}) => {
  const formData = new FormData();
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      formData.append(key, value as string | Blob);
    }
  });
  return apiRequest().post('/system/caro/create', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

export const updateCarousel = (params: {
  id: number;
  name: string;
  interact: number;
  link?: string;
  status: 1 | 0;
  publishType: number;
  startTime?: number;
  endTime?: number;
  zhTwBgPhoto?: File | string;
  zhTwTxtPhoto?: File | string;
}) => {
  const formData = new FormData();
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      formData.append(key, value as string | Blob);
    }
  });
  return apiRequest().post('/system/caro/edit', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

export const updateCarouselOrder = (params: { orders: number[] }) => {
  return apiRequest().put('/system/caro/edit/order', params);
};

export const deleteCarousel = (params: { id: number }) => {
  return apiRequest().delete('/system/caro/del', params);
};

export const updateCarouselStatus = (params: { id: number; status: number }) => {
  return apiRequest().put('/system/caro/edit/status', params);
};
