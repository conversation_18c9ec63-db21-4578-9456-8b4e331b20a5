import { createElement } from 'react';

import AccountIcon from '@/assets/img/icon/account.svg?react';
import GameManagement from '@/assets/img/icon/game-management.svg?react';
import OperationIcon from '@/assets/img/icon/operation.svg?react';
import SystemIcon from '@/assets/img/icon/system.svg?react';
import TransactionIcon from '@/assets/img/icon/transaction.svg?react';
import { Permission } from '@/enums/permissions';

type RouteItem = {
  pageName: string;
  permissionId: Permission;
};

export type Routes = {
  [key: string]: {
    icon: React.ReactNode;
    routes: RouteItem[];
    permissionId: Permission;
  };
};

const createIcon = (icon: React.FC<React.SVGProps<SVGSVGElement>>) => {
  return createElement(icon, { width: 20, height: 20, className: 'menu-icon' });
};

export const defaultRoutes: Routes = {
  player: {
    icon: createIcon(AccountIcon),
    permissionId: Permission.PLAYER,
    routes: [
      {
        pageName: 'playerlist',
        permissionId: Permission.PLAYER_LIST
      },
      {
        pageName: 'vipplayer',
        permissionId: Permission.VIP_PLAYER
      },
      {
        pageName: 'playerTag',
        permissionId: Permission.PLAYER_TAG
      }
    ]
  },
  transaction: {
    icon: createIcon(TransactionIcon),
    permissionId: Permission.TRANSACTION,
    routes: [
      {
        pageName: 'transactionRecord',
        permissionId: Permission.TRANSACTION_RECORD
      },
      {
        pageName: 'topupsetting',
        permissionId: Permission.TOP_UP_SETTING
      },
      {
        pageName: 'topuporder',
        permissionId: Permission.TOP_UP_ORDERS
      },
      {
        pageName: 'topuporderhistory',
        permissionId: Permission.TOP_UP_ORDERS_HISTORY
      },
      {
        pageName: 'unfinishGiftOrder',
        permissionId: Permission.GIFT_LIST
      },
      {
        pageName: 'completedGiftOrder',
        permissionId: Permission.COMPLETED_GIFT_ORDER
      },
      {
        pageName: 'giftSetting',
        permissionId: Permission.GIFT_SETTING
      },
      {
        pageName: 'balanceAdjustment',
        permissionId: Permission.REMAIN_CHANGE
      }
    ]
  },
  game: {
    icon: createIcon(GameManagement),
    permissionId: Permission.GAME_MANAGEMENT,
    routes: [
      {
        pageName: 'gameorder',
        permissionId: Permission.GAME_ORDER
      },
      {
        pageName: 'management',
        permissionId: Permission.GAME_SETTING
      }
    ]
  },
  operation: {
    icon: createIcon(OperationIcon),
    permissionId: Permission.OPERATION,
    routes: [
      {
        pageName: 'carousel',
        permissionId: Permission.CAROUSEL
      },
      {
        pageName: 'announcement',
        permissionId: Permission.ANNOUNCEMENT
      },
      {
        pageName: 'frontContent',
        permissionId: Permission.FRONT_CONTENT
      },
      {
        pageName: 'gameSort',
        permissionId: Permission.GAME_SORT
      },
      {
        pageName: 'mailTemplate',
        permissionId: Permission.MAIL_TEMPLATE
      }
    ]
  },
  agent: {
    icon: createIcon(SystemIcon),
    permissionId: Permission.AGENT,
    routes: [
      {
        pageName: 'admin',
        permissionId: Permission.ADMIN
      },
      {
        pageName: 'role',
        permissionId: Permission.ROLE
      },
      {
        pageName: 'log',
        permissionId: Permission.LOG
      },
      {
        pageName: 'platform',
        permissionId: Permission.PLATFORM
      },
      {
        pageName: 'recordExport',
        permissionId: Permission.RECORD_EXPORT
      }
    ]
  },
};
