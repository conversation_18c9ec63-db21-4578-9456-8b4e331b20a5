import { PublishTypeEnum } from './common';

export type Announcement = {
  id: number;
  contentMapping: {
    [key: string]: {
      title: string;
      content: string;
    };
  };
  order: number;
  status: number;
  publishType: number;
  startTime: number;
  endTime: number;
  createdBy: string;
  updatedBy: string;
  createdAt: number;
  updatedAt: number;
};

export type FrontContent = {
  id: number;
  contentMapping: {
    [key: string]: {
      title: string;
      content: string;
    };
  };
  status: number;
  order: number;
  categoryId: number;
  createdBy: string;
  updatedBy: string;
  createdAt: number;
  updatedAt: number;
  category: ContentCategory;
};

export type ContentCategory = {
  id: number;
  key: string;
  name: string;
  description: string;
};

export enum CarouselInteractEnum {
  NONE = 1,
  LINK = 2,
  INTERNAL = 3
}

export type Carousel = {
  id: number;
  name: string;
  interact: CarouselInteractEnum;
  link: string;
  imgUrlMapping: {
    [key: string]: {
      bgPhoto: string;
      txtPhoto: string;
    };
  };
  order: number;
  status: 1 | 0;
  publishType: PublishTypeEnum;
  startTime: number;
  endTime: number;
  createdBy: string;
  updatedBy: string;
  createdAt: number;
  updatedAt: number;
};

export type MailTemplate = {
  id: number;
  templateId: string;
  title: string;
  content: string;
  description: string;
  category: number;
  categoryLabel: string;
  type: number;
  typeLabel: string;
  createdBy: string;
  createdAt: number;
  updatedAt: number;
};

export type MailTemplateCategory = {
  id: number;
  name: string;
};

export type MailTemplateType = {
  id: number;
  name: string;
};

export type MailTemplateVariable = {
  key: string;
  description: string;
};
