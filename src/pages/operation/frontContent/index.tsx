import { useMutation } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import {
  deleteFrontContent,
  updateFrontContentOrder,
  updateFrontContentStatus
} from '@/api/operation';
import { ActionButtons } from '@/components/ActionButtons';
import OperatorCell from '@/components/cells/OperatorCell';
import DragTable from '@/components/DragTable';
import RButton from '@/components/RButton';
import StatusLabel from '@/components/StatusLabel';
import useActions from '@/hooks/useActions';
import TableSearchLayout from '@/layout/TableSearchLayout';
import { FrontContent } from '@/types/operation';

import FrontContentCategory from './components/FrontContentCategory.tsx';
import FrontContentContent from './components/FrontContentContent.tsx';
import FrontContentTitle from './components/FrontContentTitle.tsx';
import ContentCategoryModal from './ContentCategoryModal';
import ContentDisplayModal from './ContentDisplayModal';
import FrontContentModal from './FrontContentModal.tsx';
import useFrontContent from './hooks/useFrontContent.ts';

const TableWrap = ({
  setInitialValues,
  setOpen,
  setContentDisplayOpen,
  setSelectedContent
}: {
  setInitialValues: (value: FrontContent) => void;
  setOpen: (value: boolean) => void;
  setContentDisplayOpen: (value: boolean) => void;
  setSelectedContent: (value: FrontContent) => void;
}) => {
  const { t } = useTranslation();
  const [dataSource, setDataSource] = useState<FrontContent[]>([]);
  const { handleDelete, handleEditStatus } = useActions();

  const columns = [
    {
      title: 'pages_frontContent_title',
      dataIndex: 'title',
      key: 'title',
      render: (_: string, record: FrontContent) => {
        return <FrontContentTitle record={record} />;
      }
    },
    {
      title: 'pages_frontContent_content',
      dataIndex: 'content',
      key: 'content',
      width: 500,
      render: (_: string, record: FrontContent) => {
        return (
          <FrontContentContent
            record={record}
            onClick={() => {
              setSelectedContent(record);
              setContentDisplayOpen(true);
            }}
          />
        );
      }
    },
    {
      title: 'pages_frontContent_category',
      dataIndex: 'content',
      key: 'content',
      width: 500,
      render: (_: string, record: FrontContent) => {
        return <FrontContentCategory record={record} />;
      }
    },
    {
      title: 'common_lastOperate',
      dataIndex: 'updatedAt',
      render: (_: number, record: FrontContent) => {
        return <OperatorCell record={record} />;
      }
    },
    {
      title: 'common_status',
      dataIndex: 'status',
      render: (value: number) => {
        return <StatusLabel status={value} />;
      }
    }
  ];

  const { data, isPending, refetch: refetchFrontContent } = useFrontContent();

  useEffect(() => {
    setDataSource(data || []);
  }, [data]);

  const updateFrontContentOrderMutation = useMutation({
    mutationFn: updateFrontContentOrder,
    onError: () => {
      setDataSource(data || []);
    }
  });

  const handleChangeOrder = (data: FrontContent[]) => {
    setDataSource(data);
    updateFrontContentOrderMutation.mutate({ orders: data.map((item) => item.id) });
  };

  const deleteFrontContentMutation = useMutation({
    mutationFn: deleteFrontContent,
    onSuccess: () => {
      refetchFrontContent();
    }
  });

  const updateFrontContentMutation = useMutation({
    mutationFn: updateFrontContentStatus,
    onSuccess: () => {
      refetchFrontContent();
    }
  });

  const actionColumn = {
    title: 'common_action',
    dataIndex: 'action',
    render: (_: string, record: FrontContent) => {
      return (
        <ActionButtons
          data={record}
          buttons={['edit', 'delete', 'status']}
          onEdit={() => {
            setInitialValues(record);
            setOpen(true);
          }}
          onEditStatus={() => {
            handleEditStatus(t('pages_frontContent'), record.status, () => {
              updateFrontContentMutation.mutate({
                id: record.id,
                status: record.status === 1 ? 0 : 1
              });
            });
          }}
          isEditStatusPending={
            updateFrontContentMutation.isPending &&
            record.id === updateFrontContentMutation.variables?.id
          }
          onDelete={() => {
            handleDelete(t('pages_frontContent'), () => {
              deleteFrontContentMutation.mutate({ id: record.id });
            });
          }}
        />
      );
    }
  };

  const tableColumns = [...columns, actionColumn].map((column) => ({
    ...column,
    title: typeof column.title === 'string' ? t(column.title) : column.title
  }));

  return (
    <DragTable
      keyName="id"
      onSortChange={handleChangeOrder}
      loading={
        isPending ||
        updateFrontContentOrderMutation.isPending ||
        deleteFrontContentMutation.isPending
      }
      rowKey="id"
      dataSource={dataSource}
      columns={tableColumns}
      pagination={false}
    ></DragTable>
  );
};

const FrontContentPage = () => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const [categoryModalOpen, setCategoryModalOpen] = useState(false);
  const [contentDisplayOpen, setContentDisplayOpen] = useState(false);
  const [initialValues, setInitialValues] = useState<FrontContent>();
  const [selectedContent, setSelectedContent] = useState<FrontContent>();

  const handleAdd = () => {
    setOpen(true);
    setInitialValues(undefined);
  };

  const handleCategoryManagement = () => {
    setCategoryModalOpen(true);
  };

  return (
    <>
      <TableSearchLayout>
        <div className="mb-4 flex gap-2">
          <RButton onClick={handleAdd}>
            {t('common_add_name', { name: t('pages_frontContent') })}
          </RButton>
          <RButton onClick={handleCategoryManagement} className="!bg-green">
            {t('pages_contentCategory_management')}
          </RButton>
        </div>
        <TableWrap
          setInitialValues={setInitialValues}
          setOpen={setOpen}
          setContentDisplayOpen={setContentDisplayOpen}
          setSelectedContent={setSelectedContent}
        />
      </TableSearchLayout>
      <FrontContentModal
        open={open}
        onClose={() => setOpen(false)}
        initialValues={initialValues}
      ></FrontContentModal>
      <ContentCategoryModal open={categoryModalOpen} onClose={() => setCategoryModalOpen(false)} />
      <ContentDisplayModal
        open={contentDisplayOpen}
        onClose={() => setContentDisplayOpen(false)}
        content={selectedContent}
      />
    </>
  );
};

export default FrontContentPage;
