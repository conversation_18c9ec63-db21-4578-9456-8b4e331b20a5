import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';

import { createMailTemplate, updateMailTemplate } from '@/api/operation';
import FormModal from '@/components/FormModal';
import HTMLEditor from '@/components/HTMLEditor';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RSwitch from '@/components/RSwitch';
import { MailTemplate } from '@/types/operation';

type MailTemplateModalProps = {
  open: boolean;
  onClose: () => void;
  initialValues?: Partial<MailTemplate>;
};

const MailTemplateModal = ({ open, onClose, initialValues }: MailTemplateModalProps) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const [form] = RForm.useForm();

  const isEdit = !!initialValues?.id;

  const { mutate: createMutate, isPending: isCreatePending } = useMutation({
    mutationFn: createMailTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['mailTemplateList'] });
      onClose();
      form.resetFields();
    }
  });

  const { mutate: updateMutate, isPending: isUpdatePending } = useMutation({
    mutationFn: updateMailTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['mailTemplateList'] });
      onClose();
      form.resetFields();
    }
  });

  const handleSubmit = (values: any) => {
    const formData = {
      name: values.name,
      subject: values.subject,
      content: values.content,
      status: values.status ? 1 : 0
    };

    if (isEdit && initialValues?.id) {
      updateMutate({ ...formData, id: initialValues.id });
    } else {
      createMutate(formData);
    }
  };

  const handleClose = () => {
    form.resetFields();
    onClose();
  };

  return (
    <FormModal
      title={t(isEdit ? 'common_edit_name' : 'common_add_name', { name: t('pages_mailTemplate') })}
      form={form}
      open={open}
      onClose={handleClose}
      onSubmit={handleSubmit}
      isLoading={isCreatePending || isUpdatePending}
      initialValues={{
        name: initialValues?.name || '',
        subject: initialValues?.subject || '',
        content: initialValues?.content || '',
        status: initialValues?.status === 1
      }}
    >
      <RForm.Item
        name="name"
        label={t('pages_mailTemplate_name')}
        rules={[{ required: true, message: t('common_required') }]}
      >
        <RInput placeholder={t('pages_mailTemplate_name_placeholder')} />
      </RForm.Item>

      <RForm.Item
        name="subject"
        label={t('pages_mailTemplate_subject')}
        rules={[{ required: true, message: t('common_required') }]}
      >
        <RInput placeholder={t('pages_mailTemplate_subject_placeholder')} />
      </RForm.Item>

      <RForm.Item
        name="content"
        label={t('pages_mailTemplate_content')}
        rules={[{ required: true, message: t('common_required') }]}
      >
        <HTMLEditor />
      </RForm.Item>

      <RForm.Item name="status" label={t('common_status')} valuePropName="checked">
        <RSwitch />
      </RForm.Item>
    </FormModal>
  );
};

export default MailTemplateModal;
