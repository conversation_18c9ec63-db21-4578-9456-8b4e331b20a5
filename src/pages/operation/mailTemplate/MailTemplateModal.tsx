import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';

import {
  createMailTemplate,
  updateMailTemplate,
  getMailTemplateCategories,
  getMailTemplateTypes
} from '@/api/operation';
import FormModal from '@/components/FormModal';
import HTMLEditor from '@/components/HTMLEditor';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RSelect from '@/components/RSelect';
import { MailTemplate } from '@/types/operation';

type MailTemplateModalProps = {
  open: boolean;
  onClose: () => void;
  initialValues?: Partial<MailTemplate>;
};

const MailTemplateModal = ({ open, onClose, initialValues }: MailTemplateModalProps) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const [form] = RForm.useForm();

  const isEdit = !!initialValues?.id;

  // Fetch categories and types
  const { data: categories } = useQuery({
    queryKey: ['mailTemplateCategories'],
    queryFn: getMailTemplateCategories
  });

  const { data: types } = useQuery({
    queryKey: ['mailTemplateTypes'],
    queryFn: getMailTemplateTypes
  });

  const { mutate: createMutate, isPending: isCreatePending } = useMutation({
    mutationFn: createMailTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['mailTemplateList'] });
      onClose();
      form.resetFields();
    }
  });

  const { mutate: updateMutate, isPending: isUpdatePending } = useMutation({
    mutationFn: updateMailTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['mailTemplateList'] });
      onClose();
      form.resetFields();
    }
  });

  const handleSubmit = (values: any) => {
    const formData = {
      template_id: values.template_id,
      title: values.title,
      content: values.content,
      description: values.description,
      category: values.category,
      type: values.type
    };

    if (isEdit && initialValues?.id) {
      updateMutate({ ...formData, id: initialValues.id });
    } else {
      createMutate(formData);
    }
  };

  const handleClose = () => {
    form.resetFields();
    onClose();
  };

  const categoryOptions = categories?.data?.map(cat => ({
    label: cat.name,
    value: cat.id
  })) || [];

  const typeOptions = types?.data?.map(type => ({
    label: type.name,
    value: type.id
  })) || [];

  return (
    <FormModal
      title={t(isEdit ? 'common_edit_name' : 'common_add_name', { name: t('pages_mailTemplate') })}
      form={form}
      open={open}
      onClose={handleClose}
      onSubmit={handleSubmit}
      isLoading={isCreatePending || isUpdatePending}
      initialValues={{
        template_id: initialValues?.template_id || '',
        title: initialValues?.title || '',
        content: initialValues?.content || '',
        description: initialValues?.description || '',
        category: initialValues?.category,
        type: initialValues?.type
      }}
    >
      <RForm.Item
        name="template_id"
        label={t('pages_mailTemplate_templateId')}
        rules={[{ required: true, message: t('common_required') }]}
      >
        <RInput placeholder={t('pages_mailTemplate_templateId_placeholder')} />
      </RForm.Item>

      <RForm.Item
        name="title"
        label={t('pages_mailTemplate_title')}
        rules={[{ required: true, message: t('common_required') }]}
      >
        <RInput placeholder={t('pages_mailTemplate_title_placeholder')} />
      </RForm.Item>

      <RForm.Item
        name="category"
        label={t('pages_mailTemplate_category')}
        rules={[{ required: true, message: t('common_required') }]}
      >
        <RSelect options={categoryOptions} placeholder={t('pages_mailTemplate_category_placeholder')} />
      </RForm.Item>

      <RForm.Item
        name="type"
        label={t('pages_mailTemplate_type')}
        rules={[{ required: true, message: t('common_required') }]}
      >
        <RSelect options={typeOptions} placeholder={t('pages_mailTemplate_type_placeholder')} />
      </RForm.Item>

      <RForm.Item
        name="description"
        label={t('pages_mailTemplate_description')}
      >
        <RInput placeholder={t('pages_mailTemplate_description_placeholder')} />
      </RForm.Item>

      <RForm.Item
        name="content"
        label={t('pages_mailTemplate_content')}
        rules={[{ required: true, message: t('common_required') }]}
      >
        <HTMLEditor />
      </RForm.Item>
    </FormModal>
  );
};

export default MailTemplateModal;
