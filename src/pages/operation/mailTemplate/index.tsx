import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import RButton from '@/components/RButton';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import SearchForm from '@/components/SearchForm';
import TableSearchLayout from '@/layout/TableSearchLayout';
import { MailTemplate } from '@/types/operation';

import MailTemplateModal from './MailTemplateModal';
import MailTemplateTable from './MailTemplateTable';

type SearchFormValues = {
  template_id?: string;
  title?: string;
  category?: number;
  type?: number;
};

const SearchFormWrap = ({
  onSearch,
  onReset
}: {
  onSearch: (values: SearchFormValues) => void;
  onReset: () => void;
}) => {
  const { t } = useTranslation();

  return (
    <SearchForm<SearchFormValues> onSearch={onSearch} onReset={onReset} className="">
      <RForm.Item name="template_id" label={t('pages_mailTemplate_templateId')}>
        <RInput placeholder={t('pages_mailTemplate_templateId_placeholder')} />
      </RForm.Item>
      <RForm.Item name="title" label={t('pages_mailTemplate_title')}>
        <RInput placeholder={t('pages_mailTemplate_title_placeholder')} />
      </RForm.Item>
    </SearchForm>
  );
};

const TableWrap = ({
  setInitialValues,
  setOpen
}: {
  setInitialValues: (values: MailTemplate) => void;
  setOpen: (open: boolean) => void;
}) => {
  return <MailTemplateTable setInitialValues={setInitialValues} setOpen={setOpen} />;
};

const MailTemplatePage = () => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const [initialValues, setInitialValues] = useState<MailTemplate>();

  const handleAdd = () => {
    setOpen(true);
    setInitialValues(undefined);
  };

  const handleSearch = (values: SearchFormValues) => {
    // TODO: Implement search functionality when backend API supports it
    console.log('Search values:', values);
  };

  const handleReset = () => {
    // TODO: Implement reset functionality when backend API supports it
    console.log('Reset search');
  };

  return (
    <>
      <TableSearchLayout
        searchFields={
          <SearchFormWrap onSearch={handleSearch} onReset={handleReset} />
        }
      >
        <RButton className="mb-4" onClick={handleAdd}>
          {t('common_add_name', { name: t('pages_mailTemplate') })}
        </RButton>
        <TableWrap
          setInitialValues={setInitialValues}
          setOpen={setOpen}
        />
      </TableSearchLayout>
      <MailTemplateModal
        open={open}
        onClose={() => setOpen(false)}
        initialValues={initialValues}
      />
    </>
  );
};

export default MailTemplatePage;
