import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import RButton from '@/components/RButton';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import SearchForm from '@/components/SearchForm';
import StatusSelect from '@/components/StatusSelect';
import TableSearchLayout from '@/layout/TableSearchLayout';
import { MailTemplate } from '@/types/operation';

import MailTemplateModal from './MailTemplateModal';
import MailTemplateTable from './MailTemplateTable';

type SearchFormValues = {
  name?: string;
  subject?: string;
  status?: number;
};

const SearchFormWrap = ({
  onSearch,
  onReset
}: {
  onSearch: (values: SearchFormValues) => void;
  onReset: () => void;
}) => {
  const { t } = useTranslation();

  return (
    <SearchForm<SearchFormValues> onSearch={onSearch} onReset={onReset} className="">
      <RForm.Item name="name" label={t('pages_mailTemplate_name')}>
        <RInput placeholder={t('pages_mailTemplate_name_placeholder')} />
      </RForm.Item>
      <RForm.Item name="subject" label={t('pages_mailTemplate_subject')}>
        <RInput placeholder={t('pages_mailTemplate_subject_placeholder')} />
      </RForm.Item>
      <RForm.Item name="status" label={t('common_status')} initialValue={undefined}>
        <StatusSelect />
      </RForm.Item>
    </SearchForm>
  );
};

const TableWrap = ({
  setInitialValues,
  setOpen
}: {
  setInitialValues: (values: MailTemplate) => void;
  setOpen: (open: boolean) => void;
}) => {
  return <MailTemplateTable setInitialValues={setInitialValues} setOpen={setOpen} />;
};

const MailTemplatePage = () => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const [initialValues, setInitialValues] = useState<MailTemplate>();

  const handleAdd = () => {
    setOpen(true);
    setInitialValues(undefined);
  };

  const handleSearch = (values: SearchFormValues) => {
    // TODO: Implement search functionality when backend API supports it
    console.log('Search values:', values);
  };

  const handleReset = () => {
    // TODO: Implement reset functionality when backend API supports it
    console.log('Reset search');
  };

  return (
    <>
      <TableSearchLayout
        searchFields={
          <SearchFormWrap onSearch={handleSearch} onReset={handleReset} />
        }
      >
        <RButton className="mb-4" onClick={handleAdd}>
          {t('common_add_name', { name: t('pages_mailTemplate') })}
        </RButton>
        <TableWrap
          setInitialValues={setInitialValues}
          setOpen={setOpen}
        />
      </TableSearchLayout>
      <MailTemplateModal
        open={open}
        onClose={() => setOpen(false)}
        initialValues={initialValues}
      />
    </>
  );
};

export default MailTemplatePage;
