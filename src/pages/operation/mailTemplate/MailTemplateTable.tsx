import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { deleteMailTemplate, getMailTemplateList, updateMailTemplateStatus } from '@/api/operation';
import OperatorCell from '@/components/cells/OperatorCell';
import RButton from '@/components/RButton';
import RTable from '@/components/RTable';
import StatusLabel from '@/components/StatusLabel';
import useConfirmModal from '@/hooks/useConfirmModal';
import { MailTemplate } from '@/types/operation';
import { formatTime } from '@/utils/time';

type MailTemplateTableProps = {
  setInitialValues: (values: MailTemplate) => void;
  setOpen: (open: boolean) => void;
};

const MailTemplateTable = ({ setInitialValues, setOpen }: MailTemplateTableProps) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { confirmModal } = useConfirmModal();

  const { data: mailTemplateList, isPending } = useQuery({
    queryKey: ['mailTemplateList'],
    queryFn: getMailTemplateList
  });

  const { mutate: deleteMutate } = useMutation({
    mutationFn: deleteMailTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['mailTemplateList'] });
    }
  });

  const { mutate: updateStatusMutate } = useMutation({
    mutationFn: updateMailTemplateStatus,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['mailTemplateList'] });
    }
  });

  const handleEdit = (record: MailTemplate) => {
    setInitialValues(record);
    setOpen(true);
  };

  const handleDelete = (record: MailTemplate) => {
    confirmModal({
      content: t('common_confirm_delete'),
      onOk: () => deleteMutate({ id: record.id })
    });
  };

  const handleStatusChange = (record: MailTemplate) => {
    const newStatus = record.status === 1 ? 0 : 1;
    confirmModal({
      content: t(
        newStatus === 1 ? 'pages_admin_confirm_status_enable' : 'pages_admin_confirm_status_disable'
      ),
      onOk: () => updateStatusMutate({ id: record.id, status: newStatus })
    });
  };

  const columns = [
    {
      title: t('pages_mailTemplate_name'),
      dataIndex: 'name',
      key: 'name',
      width: '20%'
    },
    {
      title: t('pages_mailTemplate_subject'),
      dataIndex: 'subject',
      key: 'subject',
      width: '25%'
    },
    {
      title: t('common_status'),
      dataIndex: 'status',
      key: 'status',
      width: '10%',
      render: (status: 1 | 0) => <StatusLabel status={status} />
    },
    {
      title: t('common_createdBy'),
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: '12%',
      render: (createdBy: string, record: MailTemplate) => (
        <OperatorCell
          operator={createdBy}
          time={formatTime(record.createdAt)}
        />
      )
    },
    {
      title: t('common_updatedBy'),
      dataIndex: 'updatedBy',
      key: 'updatedBy',
      width: '12%',
      render: (updatedBy: string, record: MailTemplate) => (
        <OperatorCell
          operator={updatedBy}
          time={formatTime(record.updatedAt)}
        />
      )
    },
    {
      title: t('common_action'),
      key: 'action',
      width: '21%',
      render: (_: unknown, record: MailTemplate) => (
        <div className="flex gap-2">
          <RButton
            size="small"
            variant="outlined"
            color="primary"
            type="link"
            onClick={() => handleEdit(record)}
          >
            {t('common_edit')}
          </RButton>
          <RButton
            size="small"
            variant="outlined"
            color={record.status === 1 ? 'red' : 'green'}
            type="link"
            onClick={() => handleStatusChange(record)}
          >
            {record.status === 1 ? t('common_inactive') : t('common_active')}
          </RButton>
          <RButton
            size="small"
            variant="outlined"
            color="red"
            type="link"
            onClick={() => handleDelete(record)}
          >
            {t('common_delete')}
          </RButton>
        </div>
      )
    }
  ];

  return (
    <RTable
      loading={isPending}
      rowKey="id"
      dataSource={mailTemplateList?.data || []}
      columns={columns}
      pagination={false}
    />
  );
};

export default MailTemplateTable;
